import subprocess
import time
import signal
import os

# Use ['jupyter', 'lab'] for JupyterLab
command = ['jupyter', 'lab',]

# This is necessary on Windows to send Ctrl+C correctly
# It creates the process in a new process group.
creationflags = 0
if os.name == 'nt':
    creationflags = subprocess.CREATE_NEW_PROCESS_GROUP

jupyter_process = None
try:
    print("Starting Jupyter server...")
    # Start the process. The creationflags are important for Windows.
    jupyter_process = subprocess.Popen(
        command,
        creationflags=creationflags
    )
    print(f"Jupyter server running in the background (PID: {jupyter_process.pid}).")
    
    print("Main script doing work for 10 seconds...")
    time.sleep(10)
    print("Main script finished.")

finally:
    if jupyter_process and jupyter_process.poll() is None:
        print("\nAttempting to shut down Jupyter server gracefully...")
        
        # --- Send the first Ctrl+C (SIGINT) ---
        print("Sending first Ctrl+C (SIGINT)...")
        # On Windows, os.CTRL_C_EVENT is the signal
        signal_to_send = signal.CTRL_C_EVENT if os.name == 'nt' else signal.SIGINT
        jupyter_process.send_signal(signal_to_send)
        jupyter_process.send_signal(signal_to_send)

        # try:
        #     # Wait for 7 seconds for the server to shut down
        #     jupyter_process.wait(timeout=7)
        #     print("Jupyter server has been terminated successfully.")
        # except subprocess.TimeoutExpired:
        #     # --- If it's still running, send the second signal ---
        #     print("Server didn't shut down. Sending second signal (terminate)...")
        #     # Using terminate() is a more robust second step than another SIGINT
        #     jupyter_process.terminate()
        #     try:
        #         # Give it a final few seconds
        #         jupyter_process.wait(timeout=5)
        #         print("Jupyter server has been terminated.")
        #     except subprocess.TimeoutExpired:
        #         # --- As a last resort, force kill it ---
        #         print("Server did not respond to termination. Forcing shutdown...")
        #         jupyter_process.kill()
        #         print("Jupyter server has been killed.")
