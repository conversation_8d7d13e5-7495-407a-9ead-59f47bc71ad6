import argparse
import asyncio
import logging
import subprocess
import time
import sys
import os
import atexit
import json
import secrets
from pathlib import Path

from jupyter_kernel_client import KernelClient
from jupyter_nbmodel_client import (
    NbModelClient,
    get_jupyter_notebook_websocket_url,
)
from mcp.server.fastmcp import FastMCP

mcp = FastMCP("jupyter")

logger = logging.getLogger(__name__)

kernel: KernelClient
notebook: NbModelClient

# Global variable to track the started server process
_jupyter_server_process = None


def _cleanup_jupyter_server():
    """Clean up the Jupyter server process on exit."""
    global _jupyter_server_process
    if _jupyter_server_process and _jupyter_server_process.poll() is None:
        logger.info("Shutting down Jupyter server...")
        _jupyter_server_process.terminate()
        try:
            _jupyter_server_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            _jupyter_server_process.kill()
            _jupyter_server_process.wait()


def start_jupyter_server(notebook_path: str, port: int = 8888) -> tuple[str, str]:
    """
    Start a Jupyter notebook server and return the server URL and token.

    Args:
        notebook_path: Path to the notebook file to open
        port: Port to run the server on (default: 8888)

    Returns:
        tuple: (server_url, token)

    Raises:
        RuntimeError: If unable to start the server or extract connection info
    """
    global _jupyter_server_process

    # Register cleanup function
    atexit.register(_cleanup_jupyter_server)

    # Create the notebook file if it doesn't exist
    notebook_file = Path(notebook_path)
    if not notebook_file.exists():
        notebook_file.parent.mkdir(parents=True, exist_ok=True)
        # Create a minimal notebook structure
        notebook_content = {
            "cells": [],
            "metadata": {
                "kernelspec": {
                    "display_name": "Python 3",
                    "language": "python",
                    "name": "python3"
                },
                "language_info": {
                    "name": "python",
                    "version": sys.version
                }
            },
            "nbformat": 4,
            "nbformat_minor": 4
        }
        with open(notebook_file, 'w') as f:
            json.dump(notebook_content, f, indent=2)
        logger.info(f"Created new notebook: {notebook_path}")

    # Generate a simple token
    token = secrets.token_hex(32)

    # Start Jupyter server
    cmd = [
        "jupyter", "lab",
        "--no-browser",
        f"--port={port}",
        f"--notebook-dir={notebook_file.parent}",
        f"--NotebookApp.token={token}",
    ]

    logger.info(f"Starting Jupyter server with command: {' '.join(cmd)}")

    try:
        _jupyter_server_process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )

        # Wait for server to start
        server_url = f"http://localhost:{port}"

        # Give the server some time to start
        logger.info("Waiting for Jupyter server to start...")
        time.sleep(5)

        # Check if process is still running
        if _jupyter_server_process.poll() is not None:
            # Process has terminated
            stdout_output = _jupyter_server_process.stdout.read() if _jupyter_server_process.stdout else ""
            stderr_output = _jupyter_server_process.stderr.read() if _jupyter_server_process.stderr else ""
            raise RuntimeError(f"Jupyter server failed to start. STDOUT: {stdout_output}, STDERR: {stderr_output}")

        # Server should be running now

        logger.info(f"Jupyter server started successfully at {server_url}")
        return server_url, token

    except Exception as e:
        if _jupyter_server_process:
            _jupyter_server_process.terminate()
            _jupyter_server_process = None
        raise RuntimeError(f"Failed to start Jupyter server: {e}")


def get_default_notebook_path() -> str:
    """Get a default notebook path in the current working directory."""
    i = 0
    while True:
        notebook_path = os.path.join(os.getcwd(), f"Untitled{f' {i}' if i > 0 else ''}.ipynb")
        if not os.path.exists(notebook_path):
            return notebook_path
        i += 1


def extract_output(output: dict) -> str:
    """
    Extracts readable output from a Jupyter cell output dictionary.

    Args:
        output (dict): The output dictionary from a Jupyter cell.

    Returns:
        str: A string representation of the output.
    """
    output_type = output.get("output_type")
    match output_type:
        case "stream":
            return output.get("text", "")
    if output_type == "stream":
        return output.get("text", "")
    elif output_type in ["display_data", "execute_result"]:
        data = output.get("data", {})
        if "text/plain" in data:
            return data["text/plain"]
        elif "text/html" in data:
            return "[HTML Output]"
        elif "image/png" in data:
            return "[Image Output (PNG)]"
        else:
            return f"[{output_type} Data: keys={list(data.keys())}]"
    elif output_type == "error":
        return output["traceback"]
    else:
        return f"[Unknown output type: {output_type}]"


@mcp.tool()
async def execute_cell(
    index: int,
) -> list[str]:
    """Execute a notebook cell by index.
    
    Args:
        index: Cell index to execute
        
    Returns:
        list[str]: List of outputs from the executed cell
    """
    result = notebook.execute_cell(index, kernel)
    return [extract_output(output) for output in result["outputs"]]


@mcp.tool()
async def add_and_execute_code_cell(source: str) -> list[str]:
    """Add and execute a code cell in a Jupyter notebook.

    Args:
        source: Code content

    Returns:
        list[str]: List of outputs from the executed cell
    """

    index = notebook.add_code_cell(source)
    return await execute_cell(index)


@mcp.tool()
async def set_and_execute_code_cell(index: int, source: str) -> list[str]:
    """Set and execute a code cell in a Jupyter notebook.

    Args:
        index: Cell index to set
        source: New cell source

    Returns:
        list[str]: List of outputs from the executed cell
    """
    notebook.set_cell_source(index, source)
    return await execute_cell(index)


def main():
    # Disable proxy for localhost connections at the start
    original_proxies = os.environ.copy()
    try:
        # Clear proxy environment variables for localhost
        for proxy_var in ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']:
            if proxy_var in os.environ:
                del os.environ[proxy_var]

        parser = argparse.ArgumentParser(
            description="JupyMCP - Jupyter + MCP with comprehensive resource support"
        )
        parser.add_argument(
            "--server-url",
            help="Jupyter server URL (if not provided, will start a default server)"
        )
        parser.add_argument(
            "--token",
            help="Jupyter server token (if not provided, will start a default server)"
        )
        parser.add_argument(
            "--path",
            help="Path to notebook file (if not provided, will use/create default_notebook.ipynb)"
        )
        parser.add_argument(
            "--port",
            type=int,
            default=8888,
            help="Port for default Jupyter server (default: 8888)"
        )
        args = parser.parse_args()

        # Determine server connection parameters
        if args.server_url and args.token:
            # Use provided server
            server_url = args.server_url
            token = args.token
            notebook_path = args.path or get_default_notebook_path()
            logger.info(f"Using existing Jupyter server at {server_url}")
        else:
            # Start default server
            notebook_path = args.path or get_default_notebook_path()
            logger.info("Starting default Jupyter server...")
            try:
                server_url, token = start_jupyter_server(notebook_path, args.port)
            except RuntimeError as e:
                logger.error(f"Failed to start Jupyter server: {e}")
                return 1

        # Convert absolute path to relative path for the websocket URL
        notebook_file = Path(notebook_path)
        if notebook_file.is_absolute():
            # Get relative path from the notebook directory
            try:
                relative_path = notebook_file.name  # Just use the filename
            except Exception:
                relative_path = notebook_path
        else:
            relative_path = notebook_path

        logger.info(f"Using relative path for websocket: {relative_path}")

        # Try to use the collaboration API first, fall back to manual websocket URL creation
        websocket_url = None
        try:
            websocket_url = get_jupyter_notebook_websocket_url(
                server_url=server_url,
                token=token,
                path=relative_path
            )
            logger.info("Successfully created websocket URL using collaboration API")
        except Exception as e:
            logger.warning(f"Collaboration API failed: {e}")
            # Fall back to manual websocket URL creation
            # Format: ws://localhost:port/api/collaboration/room/notebook_path?token=token
            ws_server_url = server_url.replace('http://', 'ws://').replace('https://', 'wss://')
            websocket_url = f"{ws_server_url}/api/collaboration/room/{relative_path}?token={token}"
            logger.info(f"Using fallback websocket URL: {websocket_url}")

        async def amain():
            global kernel
            global notebook

            with KernelClient(server_url=server_url, token=token) as kernel:
                async with NbModelClient(websocket_url) as notebook:
                    for cell_type in ("code", "markdown", "raw"):
                        add_name = f"add_{cell_type}_cell"
                        add_func = getattr(notebook, add_name)
                        insert_name = f"insert_{cell_type}_cell"
                        insert_func = getattr(notebook, insert_name, None)
                        def add_cell(source: str, kwargs: dict | None = None) -> int:
                            return add_func(source, **(kwargs or {}))
                        def insert_cell(index: int, source: str, kwargs: dict | None = None):
                            insert_func(index, source, **(kwargs or {}))
                            return f"{cell_type.capitalize()} cell inserted."
                        mcp.tool(f"add_{cell_type}_cell", add_func.__doc__)(add_cell)
                        if insert_func is not None:
                            mcp.tool(f"insert_{cell_type}_cell", insert_func.__doc__)(insert_cell)
                    mcp.tool()(notebook.get_cell_metadata)
                    mcp.tool()(notebook.get_cell_source)
                    mcp.tool()(notebook.get_notebook_metadata)
                    mcp.tool()(notebook.set_cell_metadata)
                    mcp.tool()(notebook.set_cell_source)
                    mcp.tool()(notebook.set_notebook_metadata)
                    await mcp.run_stdio_async()

        try:
            asyncio.run(amain())
            return 0
        except KeyboardInterrupt:
            logger.info("Received interrupt signal, shutting down...")
            return 0
        except Exception as e:
            logger.error(f"Error running MCP server: {e}")
            return 1
    finally:
        # Restore original proxy settings
        os.environ.clear()
        os.environ.update(original_proxies)


if __name__ == "__main__":
    main()
